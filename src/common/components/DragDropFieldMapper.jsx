import React, { useState, useCallback } from 'react'
import { Card, Row, Col, Tag, Button, Space, Typography, Divider, App } from 'antd'
import { DragOutlined, DeleteOutlined, CheckOutlined } from '@ant-design/icons'

const { Title, Text } = Typography

/**
 * Drag and Drop Field Mapper Component
 * Allows users to map available data fields to PDF form fields through drag and drop
 *
 * @param {Object} props - Component props
 * @param {Array} props.pdfFields - Array of PDF form fields extracted from template
 * @param {Object} props.availableFields - Available data fields for mapping
 * @param {Object} props.fieldMappings - Current field mappings
 * @param {Function} props.onMappingChange - Callback when mappings change
 * @param {Function} props.onApplyMappings - Callback to apply mappings to form
 */
const DragDropFieldMapper = ({
  pdfFields = [],
  availableFields = {},
  fieldMappings = {},
  onMappingChange,
  onApplyMappings,
}) => {
  const { message } = App.useApp()
  const [draggedField, setDraggedField] = useState(null)
  const [dragOverTarget, setDragOverTarget] = useState(null)

  // Handle drag start for available fields
  const handleDragStart = useCallback((e, fieldKey, fieldValue) => {
    setDraggedField({ key: fieldKey, value: fieldValue })
    e.dataTransfer.effectAllowed = 'copy'
    e.dataTransfer.setData('text/plain', fieldKey)
  }, [])

  // Handle drag over PDF fields
  const handleDragOver = useCallback((e, pdfFieldName) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'copy'
    setDragOverTarget(pdfFieldName)
  }, [])

  // Handle drag leave
  const handleDragLeave = useCallback(() => {
    setDragOverTarget(null)
  }, [])

  // Handle drop on PDF fields
  const handleDrop = useCallback(
    (e, pdfFieldName) => {
      e.preventDefault()
      setDragOverTarget(null)

      if (draggedField) {
        const newMappings = {
          ...fieldMappings,
          [pdfFieldName]: draggedField.key,
        }
        onMappingChange(newMappings)
        message.success(`Đã ánh xạ "${draggedField.key}" với trường PDF "${pdfFieldName}"`)
      }
      setDraggedField(null)
    },
    [draggedField, fieldMappings, onMappingChange, message],
  )

  // Handle removing a mapping
  const handleRemoveMapping = useCallback(
    (pdfFieldName) => {
      const newMappings = { ...fieldMappings }
      delete newMappings[pdfFieldName]
      onMappingChange(newMappings)
      message.success(`Đã xóa ánh xạ cho trường "${pdfFieldName}"`)
    },
    [fieldMappings, onMappingChange, message],
  )

  // Handle applying all mappings
  const handleApplyMappings = useCallback(() => {
    if (Object.keys(fieldMappings).length === 0) {
      message.warning('Chưa có ánh xạ nào để áp dụng')
      return
    }
    onApplyMappings(fieldMappings)
    message.success('Đã áp dụng tất cả ánh xạ vào form')
  }, [fieldMappings, onApplyMappings, message])

  return (
    <div>
      <Row gutter={[16, 16]}>
        {/* Available Fields Panel */}
        <Col span={12}>
          <Card
            title={
              <Space>
                <DragOutlined />
                <span>Dữ liệu có sẵn</span>
              </Space>
            }
            size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Kéo các trường dữ liệu bên dưới vào các trường PDF để tạo ánh xạ
            </Text>
            <Divider style={{ margin: '12px 0' }} />
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                {Object.entries(availableFields).map(([key, value]) => (
                  <Tag
                    key={key}
                    draggable
                    onDragStart={(e) => handleDragStart(e, key, value)}
                    style={{
                      cursor: 'grab',
                      padding: '6px 10px',
                      margin: '2px',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                      display: 'block',
                      backgroundColor: '#fafafa',
                      transition: 'all 0.2s ease',
                      userSelect: 'none',
                    }}
                    onMouseDown={(e) => (e.target.style.cursor = 'grabbing')}
                    onMouseUp={(e) => (e.target.style.cursor = 'grab')}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = '#e6f7ff'
                      e.target.style.borderColor = '#1890ff'
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = '#fafafa'
                      e.target.style.borderColor = '#d9d9d9'
                    }}>
                    <DragOutlined style={{ marginRight: '6px', color: '#1890ff' }} />
                    <strong>{key}:</strong> {String(value || '').substring(0, 40)}
                    {String(value || '').length > 40 && '...'}
                  </Tag>
                ))}
              </Space>
            </div>
          </Card>
        </Col>

        {/* PDF Fields Panel */}
        <Col span={12}>
          <Card
            title={
              <Space>
                <span>Trường PDF ({pdfFields.length})</span>
                <Button
                  type="primary"
                  size="small"
                  icon={<CheckOutlined />}
                  onClick={handleApplyMappings}
                  disabled={Object.keys(fieldMappings).length === 0}>
                  Áp dụng ánh xạ
                </Button>
              </Space>
            }
            size="small">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Thả các trường dữ liệu vào đây để tạo ánh xạ
            </Text>
            <Divider style={{ margin: '12px 0' }} />
            <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
              <Space direction="vertical" style={{ width: '100%' }} size="small">
                {pdfFields.map((field) => {
                  const mappedField = fieldMappings[field.name]
                  const mappedValue = mappedField ? availableFields[mappedField] : null
                  const isDropTarget = dragOverTarget === field.name

                  return (
                    <div
                      key={field.name}
                      onDragOver={(e) => handleDragOver(e, field.name)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, field.name)}
                      style={{
                        padding: '8px',
                        border: `2px dashed ${isDropTarget ? '#1890ff' : mappedField ? '#52c41a' : '#d9d9d9'}`,
                        borderRadius: '4px',
                        backgroundColor: isDropTarget
                          ? '#e6f7ff'
                          : mappedField
                            ? '#f6ffed'
                            : '#fafafa',
                        minHeight: '60px',
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        transition: 'all 0.3s ease',
                      }}>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                        }}>
                        <div>
                          <Text strong style={{ fontSize: '13px' }}>
                            {field.name}
                          </Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: '11px' }}>
                            {field.type}
                          </Text>
                        </div>
                        {mappedField && (
                          <Button
                            type="text"
                            size="small"
                            icon={<DeleteOutlined />}
                            onClick={() => handleRemoveMapping(field.name)}
                            style={{ color: '#ff4d4f' }}
                          />
                        )}
                      </div>
                      {mappedField && (
                        <div style={{ marginTop: '4px' }}>
                          <Tag color="green" style={{ fontSize: '11px' }}>
                            {mappedField}: {String(mappedValue || '').substring(0, 30)}
                            {String(mappedValue || '').length > 30 && '...'}
                          </Tag>
                        </div>
                      )}
                      {isDropTarget && (
                        <Text
                          type="secondary"
                          style={{ fontSize: '11px', fontStyle: 'italic', textAlign: 'center' }}>
                          Thả vào đây để tạo ánh xạ
                        </Text>
                      )}
                    </div>
                  )
                })}
              </Space>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Mapping Summary */}
      {Object.keys(fieldMappings).length > 0 && (
        <Card title="Tóm tắt ánh xạ" size="small" style={{ marginTop: '16px' }}>
          <Space wrap>
            {Object.entries(fieldMappings).map(([pdfField, dataField]) => (
              <Tag key={pdfField} color="blue">
                {pdfField} ← {dataField}
              </Tag>
            ))}
          </Space>
        </Card>
      )}
    </div>
  )
}

export default DragDropFieldMapper

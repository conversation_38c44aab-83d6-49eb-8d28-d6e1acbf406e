# Drag & Drop PDF Field Configuration

## Tổng quan

Tính năng kéo thả cấu hình PDF cho phép người dùng dễ dàng ánh xạ các trường dữ liệu có sẵn với các trường trong PDF template thông qua giao diện kéo thả trực quan.

## Cách sử dụng

### 1. Chọn PDF Template
- Trong ApprovalLetterForm, chọn template PDF từ dropdown "Chọn Template"
- H<PERSON> thống sẽ tự động trích xuất các trường form từ PDF

### 2. <PERSON><PERSON><PERSON> hình trường dữ liệu
Có 2 cách để cấu hình:

#### A. <PERSON><PERSON><PERSON> thả cấu hình (Khuyến nghị)
1. <PERSON><PERSON><PERSON><PERSON> sang tab "Kéo thả cấu hình"
2. <PERSON><PERSON><PERSON> trái: <PERSON><PERSON> sách các trường dữ liệu có sẵn (fullName, sex, dob, etc.)
3. <PERSON><PERSON><PERSON> phải: <PERSON><PERSON> sách các trường PDF được trích xuất từ template
4. <PERSON><PERSON>o trường dữ liệu từ bên trái thả vào trường PDF bên phải
5. Bấm "Áp dụng ánh xạ" để áp dụng tất cả ánh xạ vào form

#### B. Nhập thủ công
1. Chuyển sang tab "Nhập thủ công"
2. Nhập giá trị trực tiếp vào từng trường PDF

### 3. Preview và Lưu
1. Bấm "Preview" để xem PDF hoàn chỉnh
2. Bấm "Lưu vào DocumentStore" để lưu PDF và cấu hình

## Dữ liệu có sẵn

Các trường dữ liệu được tự động lấy từ:
- **Thông tin bệnh nhân**: fullName, sex, dob, hn, phone, cccd
- **Thông tin thẻ BHYT**: card, cardDate, address
- **Thông tin khám**: visitId, departmentName
- **Thông tin hệ thống**: currentDate, currentDateTime, hospitalName

## Lưu trữ cấu hình

- Cấu hình ánh xạ được lưu trong cột `note` của bảng `medical_record_form`
- Định dạng JSON bao gồm:
  - `pdfFieldValues`: Giá trị các trường PDF
  - `fieldMappings`: Ánh xạ giữa trường PDF và trường dữ liệu
  - `templateId`: ID của template được sử dụng
  - `lastSavedFileName`: Tên file PDF được lưu
  - `lastSavedAt`: Thời gian lưu

## Tính năng nổi bật

1. **Giao diện trực quan**: Kéo thả dễ dàng với hiệu ứng visual feedback
2. **Tự động ánh xạ**: Hệ thống ghi nhớ cấu hình cho lần sử dụng sau
3. **Preview real-time**: Xem trước PDF ngay lập tức
4. **Hỗ trợ tiếng Việt**: Font tùy chỉnh cho ký tự tiếng Việt
5. **Lưu trữ tự động**: Cấu hình được lưu cùng với PDF

## Cấu trúc code

- `PopulatePDF.jsx`: Component chính quản lý PDF và tabs
- `DragDropFieldMapper.jsx`: Component xử lý kéo thả
- `ApprovalLetterForm.jsx`: Form chính tích hợp tính năng

## Mở rộng

Để thêm trường dữ liệu mới:
1. Cập nhật object `availableFields` trong `ApprovalLetterForm.jsx`
2. Trường sẽ tự động xuất hiện trong giao diện kéo thả

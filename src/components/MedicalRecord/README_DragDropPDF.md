# Drag & Drop PDF Field Positioning

## Tổng quan

Tính năng kéo thả vào PDF cho phép người dùng kéo các trường dữ liệu trực tiếp vào giao diện PDF để xác định vị trí chính xác (x, y, page) cho từng trường. Hệ thống hỗ trợ cấu hình mặc định cho từng template và cho phép tùy chỉnh vị trí theo nhu cầu.

## Cách sử dụng

### 1. Chọn PDF Template
- Trong ApprovalLetterForm, chọn template PDF từ dropdown "Chọn Template"
- <PERSON>ệ thống sẽ tự động tải PDF template và hiển thị giao diện kéo thả

### 2. Đặt vị trí trường dữ liệu
Có 2 cách để cấu hình:

#### A. Kéo thả vào PDF (Khuyến nghị)
1. <PERSON><PERSON><PERSON><PERSON> sang tab "Kéo thả vào PDF"
2. **Bên trái**: Panel điều khiển với:
   - <PERSON><PERSON> sách các trường dữ liệu có sẵn (fullName, sex, dob, etc.)
   - Nút "Thêm trường mặc định" để áp dụng cấu hình có sẵn
   - Nút "Preview PDF" để xem kết quả
   - Danh sách các trường đã được đặt vị trí
3. **Bên phải**: Giao diện PDF với khả năng nhận drop
4. **Thao tác**:
   - Kéo trường dữ liệu từ panel trái thả trực tiếp vào vị trí mong muốn trên PDF
   - Hệ thống tự động tính toán tọa độ x, y và số trang
   - Các trường đã đặt sẽ hiển thị dưới dạng nhãn màu xanh trên PDF

#### B. Nhập thủ công (Dự phòng)
1. Chuyển sang tab "Nhập thủ công"
2. Nhập giá trị trực tiếp vào các trường form PDF (nếu có)

### 3. Quản lý vị trí trường
- **Xem danh sách**: Panel bên trái hiển thị tất cả trường đã đặt với tọa độ chính xác
- **Xóa trường**: Bấm nút xóa (🗑️) bên cạnh mỗi trường để loại bỏ
- **Thêm mặc định**: Bấm "Thêm trường mặc định" để áp dụng cấu hình có sẵn của template

### 4. Preview và Lưu
1. Bấm "Preview PDF" để xem PDF với các trường đã được điền
2. Bấm "Lưu vào DocumentStore" để lưu PDF và cấu hình vị trí

## Dữ liệu có sẵn

Các trường dữ liệu được tự động lấy từ:
- **Thông tin bệnh nhân**: fullName, sex, dob, hn, phone, cccd
- **Thông tin thẻ BHYT**: card, cardDate, address
- **Thông tin khám**: visitId, departmentName
- **Thông tin hệ thống**: currentDate, currentDateTime, hospitalName

## Lưu trữ cấu hình

- Cấu hình vị trí được lưu trong cột `note` của bảng `medical_record_form`
- Định dạng JSON bao gồm:
  - `fieldPositions`: Mảng các vị trí trường với x, y, page, fieldKey, value
  - `templateId`: ID của template được sử dụng
  - `lastSavedFileName`: Tên file PDF được lưu
  - `lastSavedAt`: Thời gian lưu

## Cấu hình mặc định

Mỗi template có thể có cấu hình vị trí mặc định:
```javascript
const defaultFieldPositions = {
  approval_letter_vn: {
    fullName: { x: 150, y: 200, page: 1 },
    sex: { x: 300, y: 200, page: 1 },
    dob: { x: 450, y: 200, page: 1 },
    // ... các trường khác
  }
}
```

## Tính năng nổi bật

1. **Kéo thả trực tiếp**: Thả trường vào vị trí chính xác trên PDF
2. **Tính toán tọa độ tự động**: Hệ thống tự động chuyển đổi tọa độ màn hình sang PDF
3. **Hiển thị real-time**: Các trường đã đặt hiển thị ngay trên PDF
4. **Cấu hình mặc định**: Template có sẵn vị trí cho các trường phổ biến
5. **Hỗ trợ tiếng Việt**: Font tùy chỉnh cho ký tự tiếng Việt
6. **Quản lý vị trí**: Xem, chỉnh sửa, xóa các vị trí đã đặt

## Cấu trúc code

- `PopulatePDF.jsx`: Component chính quản lý PDF và tabs
- `DragDropFieldMapper.jsx`: Component quản lý trường và vị trí
- `DroppablePdfViewer.jsx`: PDF viewer hỗ trợ drag & drop
- `ApprovalLetterForm.jsx`: Form chính tích hợp tính năng

## Mở rộng

### Thêm trường dữ liệu mới:
1. Cập nhật object `availableFields` trong `ApprovalLetterForm.jsx`
2. Trường sẽ tự động xuất hiện trong panel kéo thả

### Thêm template mới:
1. Thêm template vào array `pdfTemplates`
2. Thêm cấu hình mặc định vào `defaultFieldPositions`
3. Template sẽ tự động có sẵn các vị trí mặc định

### Tùy chỉnh vị trí mặc định:
- Chỉnh sửa tọa độ x, y, page trong object `defaultFieldPositions`
- Tọa độ được tính theo đơn vị pixel với gốc tọa độ ở góc trên-trái
